defmodule Mqttable.MqttClient.Manager do
  @moduledoc """
  DynamicSupervisor for MQTT client worker processes.
  This module manages individual worker processes for each MQTT client connection.
  """
  use DynamicSupervisor
  require Logger

  alias Mqttable.MqttClient.{Worker, EtsOperations}

  # Client status values
  @status_disconnected :disconnected

  # Type definitions
  @type client_id :: String.t()
  @type client_pid :: pid()
  @type client_status :: :connected | :disconnected | :reconnecting | :connecting
  @type emqtt_status :: atom()
  @type error_reason :: atom() | {atom(), term()}
  @type error_message :: String.t()

  # Client API
  def start_link(opts \\ []) do
    DynamicSupervisor.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  Connects to an MQTT broker using the specified connection parameters.
  Returns {:ok, client_id} if successful, {:error, reason, error_message} otherwise.
  """
  @spec connect(map(), map()) ::
          {:ok, client_id()}
          | {:error, error_reason(), error_message()}
  def connect(connection, broker) do
    client_id = connection.client_id

    # Check if client is already connected
    case EtsOperations.lookup_client_status(client_id) do
      @status_disconnected ->
        start_new_worker(connection, broker)

      _ ->
        # Client exists, check if startup arguments are the same
        new_args = {Map.delete(broker, :connections), connection}

        case Worker.get_startup_args(client_id) do
          {:ok, current_args} ->
            handle_existing_worker(client_id, current_args, new_args, connection, broker)

          {:error, :not_found} ->
            # Worker not found, start new one
            start_new_worker(connection, broker)
        end
    end
  end

  @doc """
  Disconnects from an MQTT broker.
  Returns :ok when completed (always succeeds).
  """
  @spec disconnect(client_id()) :: :ok
  def disconnect(client_id) do
    case EtsOperations.lookup_client_record(client_id) do
      {@status_disconnected, _, _, _} ->
        :ok

      {_, worker_pid, _, _} when worker_pid != nil ->
        # Terminate the worker process
        case DynamicSupervisor.terminate_child(__MODULE__, worker_pid) do
          :ok -> :ok
          {:error, :not_found} -> :ok
        end

      {_, nil, _, _} ->
        # No active worker PID, remove from ETS and consider it disconnected
        EtsOperations.remove_client_record(client_id)
        :ok
    end
  end

  @doc """
  Subscribes to an MQTT topic.
  Returns {:ok, properties, reason_codes} if successful, {:error, reason} otherwise.
  """
  @spec subscribe(client_id(), binary(), keyword()) ::
          {:ok, map(), list()} | {:error, :not_connected} | {:error, any(), String.t()}
  def subscribe(client_id, topic, opts \\ []) do
    Worker.subscribe(client_id, topic, opts)
  end

  @doc """
  Unsubscribes from an MQTT topic.
  Returns {:ok, properties, reason_code} if successful, {:error, reason} otherwise.
  """
  @spec unsubscribe(client_id(), binary()) ::
          {:ok, map(), list()} | {:error, :not_connected} | {:error, any(), String.t()}
  def unsubscribe(client_id, topic) do
    Worker.unsubscribe(client_id, topic)
  end

  @doc """
  Publishes a message to an MQTT topic.
  Returns {:ok, packet_id} if successful, {:error, reason} otherwise.
  For QoS 0 messages, packet_id will be 0.
  """
  @spec publish(client_id(), binary(), binary(), keyword()) ::
          {:ok, integer()} | {:error, :not_connected} | {:error, any(), String.t()}
  def publish(client_id, topic, payload, opts \\ []) do
    Worker.publish(client_id, topic, payload, opts)
  end

  @doc """
  Gets the status of an MQTT client.
  Returns :connected, :disconnected, or :reconnecting.
  """
  @spec get_status(client_id()) :: client_status()
  def get_status(client_id) do
    lookup_client_status(client_id)
  end

  @doc """
  Gets all active MQTT clients.
  Returns a map of client_id to client_pid.
  """
  @spec get_all_clients() :: %{client_id() => client_pid()}
  def get_all_clients do
    EtsOperations.get_all_clients()
  end

  @doc """
  Gets all connected MQTT clients.
  Returns a list of maps with client information including MQTT version.
  """
  @spec get_connected_clients() :: [
          %{client_id: client_id(), status: client_status(), mqtt_version: String.t()}
        ]
  def get_connected_clients do
    EtsOperations.get_connected_clients()
  end

  @doc """
  Starts workers for all saved connections.
  This should be called at application startup to restore saved connections.
  Each worker will decide whether to actually connect based on its saved status.
  """
  def start_saved_connections(connection_sets) do
    try do
      # Start workers for all saved connections
      Enum.each(connection_sets, fn connection_set ->
        broker = Map.delete(connection_set, :connections)
        connections = connection_set[:connections] || []

        Enum.each(connections, fn connection ->
          client_id = connection.client_id

          # Check if worker already exists
          case EtsOperations.lookup_client_status(client_id) do
            @status_disconnected ->
              # No worker exists, start one
              args = {broker, connection}

              case DynamicSupervisor.start_child(__MODULE__, {Worker, args}) do
                {:ok, _worker_pid} ->
                  Logger.info("Started worker for saved connection: #{client_id}")

                {:error, {:already_started, _worker_pid}} ->
                  Logger.debug("Worker already exists for client: #{client_id}")

                {:error, reason} ->
                  Logger.warning(
                    "Failed to start worker for saved connection #{client_id}: #{inspect(reason)}"
                  )
              end

            _ ->
              # Worker already exists
              Logger.debug("Worker already exists for saved connection: #{client_id}")
          end
        end)
      end)
    catch
      :exit, reason ->
        Logger.error("Exit while starting saved connections: #{inspect(reason)}")

      :error, reason ->
        Logger.error("Error while starting saved connections: #{inspect(reason)}")

      type, reason ->
        Logger.error(
          "Unexpected error while starting saved connections: #{type} #{inspect(reason)}"
        )
    end

    :ok
  end

  # ETS record structure
  # {client_id, worker_pid, client_pid, mqtt_opts, parse_state, status}

  # DynamicSupervisor callbacks

  @impl true
  @spec init(keyword()) :: {:ok, DynamicSupervisor.sup_flags()}
  def init(_opts) do
    # Create ETS table for clients and status
    # Each record will be {client_id, worker_pid, client_pid, mqtt_opts, parse_state, status}
    :ets.new(__MODULE__, [:set, :public, :named_table])

    # Use DynamicSupervisor strategy to manage worker processes
    DynamicSupervisor.init(strategy: :one_for_one)
  end

  # ETS helper functions for client records

  @doc """
  Stores a client record in the ETS table.
  """
  def store_client_record(client_id, worker_pid, client_pid, mqtt_opts, status) do
    # Handle nil mqtt_opts by providing defaults
    opts = mqtt_opts || []

    max_size = Map.get(Keyword.get(opts, :properties, %{}), :"Receive-Maximum", 0xFFFFFFF)

    ver =
      case Keyword.get(opts, :proto_ver, :v5) do
        :v5 -> 5
        _ -> 4
      end

    parse_state = :emqtt_frame.initial_parse_state(%{max_size: max_size, version: ver})
    # ETS record structure: {client_id, worker_pid, client_pid, mqtt_opts, parse_state, status}
    :ets.insert(__MODULE__, {client_id, worker_pid, client_pid, opts, parse_state, status})
  end

  @doc """
  Removes a client record from the ETS table.
  """
  def remove_client_record(client_id) do
    EtsOperations.remove_client_record(client_id)
  end

  # ETS helper functions for client records

  defp lookup_client_status(client_id) do
    EtsOperations.lookup_client_status(client_id)
  end

  def lookup_client_parse_state(client_id) do
    EtsOperations.lookup_client_parse_state(client_id)
  end

  def store_client_parse_state(client_id, parse_state) do
    EtsOperations.store_client_parse_state(client_id, parse_state)
  end

  # Helper function to start a new worker
  defp start_new_worker(connection, broker) do
    client_id = connection.client_id
    args = {Map.delete(broker, :connections), connection}

    case DynamicSupervisor.start_child(__MODULE__, {Worker, args}) do
      {:ok, _worker_pid} ->
        {:ok, client_id}

      {:error, {:already_started, _worker_pid}} ->
        {:ok, client_id}

      {:error, reason} ->
        {:error, reason, "Failed to start worker process"}
    end
  end

  # Helper function to compare startup arguments
  defp startup_args_equal?(current_args, new_args) do
    current_args == new_args
  end

  defp handle_existing_worker(client_id, current_args, new_args, connection, broker) do
    if startup_args_equal?(current_args, new_args) do
      # Arguments are the same, keep existing worker
      {:ok, client_id}
    else
      # Arguments are different, restart worker with new arguments
      Logger.info(
        "Restarting worker for client #{client_id} due to changed startup arguments"
      )

      case disconnect(client_id) do
        :ok ->
          start_new_worker(connection, broker)
          
        _ ->
          {:error, "Failed to disconnect existing worker"}
      end
    end
  end
end
