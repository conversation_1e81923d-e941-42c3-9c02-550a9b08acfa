defmodule Mqttable.Settings.Storage do
  @moduledoc """
  Module for handling the persistence of application settings to disk.
  """

  require Logger

  @data_dir "priv/data"
  @settings_file Path.join(@data_dir, "settings.json")

  @doc """
  Saves settings to JSON file.

  ## Parameters

  - `settings`: Map containing settings to be saved

  ## Returns

  - `{:ok, path}`: On successful save, returns the path where the file was saved
  - `{:error, reason}`: On failure, returns the error reason
  """
  def save(settings) when is_map(settings) do
    # Ensure the data directory exists
    File.mkdir_p!(@data_dir)

    # Convert settings to JSON
    case Jason.encode(settings, pretty: true) do
      {:ok, json} ->
        # Write JSON to file
        case File.write(@settings_file, json) do
          :ok -> {:ok, @settings_file}
          {:error, reason} -> {:error, "Failed to write settings file: #{reason}"}
        end

      {:error, reason} ->
        {:error, "Failed to encode settings to JSON: #{inspect(reason)}"}
    end
  end

  @doc """
  Loads settings from JSON file.

  ## Returns

  - `{:ok, settings}`: On successful load, returns the settings map
  - `{:error, reason}`: On failure, returns the error reason
  """
  def load do
    if File.exists?(@settings_file) do
      with {:ok, json} <- File.read(@settings_file),
           {:ok, settings} <- Jason.decode(json, keys: :atoms) do
        # Convert string keys to atoms for nested maps if needed
        settings = deep_atomize_keys(settings)
        {:ok, settings}
      else
        {:error, %Jason.DecodeError{} = reason} ->
          {:error, "Failed to decode settings JSON: #{inspect(reason)}"}
        {:error, reason} ->
          {:error, "Failed to read settings file: #{reason}"}
      end
    else
      # If file doesn't exist, return default settings
      {:ok, default_settings()}
    end
  end

  @doc """
  Returns the default settings.
  """
  def default_settings do
    %{
      time_zone: Application.get_env(:mqttable, :time_zone, "UTC"),
      max_messages_per_broker: 3000
    }
  end

  # Private function to recursively convert string keys to atoms
  defp deep_atomize_keys(map) when is_map(map) do
    map
    |> Enum.map(fn
      {key, value} when is_binary(key) -> {String.to_atom(key), deep_atomize_keys(value)}
      {key, value} -> {key, deep_atomize_keys(value)}
    end)
    |> Enum.into(%{})
  end

  defp deep_atomize_keys(list) when is_list(list) do
    Enum.map(list, &deep_atomize_keys/1)
  end

  defp deep_atomize_keys(value), do: value
end
