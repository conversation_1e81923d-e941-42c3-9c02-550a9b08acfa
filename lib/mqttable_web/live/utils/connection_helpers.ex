defmodule MqttableWeb.Utils.ConnectionHelpers do
  @moduledoc """
  Helper functions for connection-related operations.
  This module provides utility functions for working with connections and connection sets.
  """

  alias Mqttable.Encryption

  # Default ports for different protocols
  @default_ports %{
    "mqtt" => "1883",
    "mqtts" => "1884",
    "ws" => "8083",
    "wss" => "8084",
    "quic" => "4567"
  }

  @doc """
  Generates a display name for a connection set.
  Formats the name as host:port, omitting the port if it's the default for the protocol.
  Truncates the name if it's too long.
  """
  def display_name(connection_set) do
    # Maximum length for display name
    max_length = 24

    cond do
      Map.has_key?(connection_set, :host) and Map.has_key?(connection_set, :port) and
          Map.has_key?(connection_set, :protocol) ->
        # Get the default port for this protocol
        default_port = Map.get(@default_ports, connection_set.protocol)
        host = connection_set.host

        # Only show port if it's not the default for the protocol
        if connection_set.port == default_port do
          # No port to display, just truncate the host if needed
          if String.length(host) > max_length do
            String.slice(host, 0, max_length - 3) <> "..."
          else
            host
          end
        else
          # Need to display the port, so reserve space for it
          port_part = ":#{connection_set.port}"
          port_length = String.length(port_part)
          max_host_length = max_length - port_length

          if String.length(host) > max_host_length do
            # Truncate the host to fit the port
            truncated_host = String.slice(host, 0, max_host_length - 3) <> "..."
            "#{truncated_host}#{port_part}"
          else
            "#{host}#{port_part}"
          end
        end

      Map.has_key?(connection_set, :host) and Map.has_key?(connection_set, :port) ->
        # Fallback for backward compatibility
        host = connection_set.host
        port_part = ":#{connection_set.port}"
        port_length = String.length(port_part)
        max_host_length = max_length - port_length

        if String.length(host) > max_host_length do
          truncated_host = String.slice(host, 0, max_host_length - 3) <> "..."
          "#{truncated_host}#{port_part}"
        else
          "#{host}#{port_part}"
        end

      true ->
        "Unknown"
    end
  end

  @doc """
  Generates a full connection URL for tooltip.
  """
  def connection_url(connection_set) do
    if Map.has_key?(connection_set, :protocol) and Map.has_key?(connection_set, :host) and
        Map.has_key?(connection_set, :port) do
      base_url = "#{connection_set.protocol}://#{connection_set.host}:#{connection_set.port}"

      # Add WebSocket path for ws/wss protocols
      case connection_set.protocol do
        protocol when protocol in ["ws", "wss"] ->
          ws_path = Map.get(connection_set, :ws_path, "/mqtt")
          base_url <> ws_path

        _ ->
          base_url
      end
    else
      "Unknown connection"
    end
  end

  @doc """
  Converts color name to CSS background class.
  """
  def color_class(color) do
    case color do
      "blue" -> "bg-blue-500"
      "green" -> "bg-green-500"
      "red" -> "bg-red-500"
      "yellow" -> "bg-yellow-500"
      "purple" -> "bg-purple-500"
      _ -> "bg-blue-500"
    end
  end

  @doc """
  Converts color name to CSS text color class for icons.
  """
  def icon_color_class(color) do
    case color do
      "blue" -> "text-blue-500"
      "green" -> "text-green-500"
      "red" -> "text-red-500"
      "yellow" -> "text-yellow-500"
      "purple" -> "text-purple-500"
      _ -> "text-blue-500"
    end
  end

  @doc """
  Determines connection status.
  This is a placeholder - in a real implementation, this would check actual connection status.
  """
  def connection_status(_set) do
    # For now, we'll randomly return a status for demonstration
    # In a real implementation, this would check the actual connection status
    statuses = [:online, :connecting, :offline]
    Enum.random(statuses)
  end

  @doc """
  Finds a connection set by name.
  Uses only the name field as the key for lookup.
  """
  def find_connection_set_by_name(connection_sets, name) do
    # Find by name directly
    Enum.find(connection_sets, fn set -> Map.get(set, :name) == name end)
  end

  @doc """
  Selects an appropriate active broker from the available connection sets.
  Returns the first available broker if current active broker is not found.
  Returns nil if no brokers exist.
  """
  def select_active_broker(connection_sets, current_active_name \\ nil) do
    cond do
      # No brokers available
      Enum.empty?(connection_sets) ->
        nil

      # Current active broker still exists
      current_active_name && find_connection_set_by_name(connection_sets, current_active_name) ->
        find_connection_set_by_name(connection_sets, current_active_name)

      # Select first available broker
      true ->
        List.first(connection_sets)
    end
  end

  @doc """
  Adds an empty variable row to a connection set.
  """
  def add_empty_variable_row(nil), do: nil

  def add_empty_variable_row(set) do
    # Ensure variables exist
    variables = Map.get(set, :variables, [])

    # If there are already variables, don't add an empty row
    if length(variables) > 0 do
      set
    else
      # If no variables exist, add an empty row
      empty_var = %{name: "", value: "", enabled: true}
      updated_vars = [empty_var]
      Map.put(set, :variables, updated_vars)
    end
  end

  @doc """
  Identifies duplicate variable names and assigns colors.
  """
  def get_duplicate_colors(variables) do
    # Count occurrences of each name
    name_counts =
      variables
      |> Enum.filter(fn var -> var.name != "" end)
      |> Enum.group_by(fn var -> var.name end)
      |> Enum.map(fn {name, vars} -> {name, length(vars)} end)
      |> Enum.into(%{})

    # Assign colors to duplicate names
    duplicate_colors =
      name_counts
      |> Enum.filter(fn {_name, count} -> count > 1 end)
      |> Enum.with_index()
      |> Enum.map(fn {{name, _count}, index} ->
        color = get_duplicate_color(index)
        {name, color}
      end)
      |> Enum.into(%{})

    duplicate_colors
  end

  @doc """
  Gets a color for a duplicate based on index.
  """
  def get_duplicate_color(index) do
    colors = ["text-error", "text-warning", "text-info", "text-success", "text-secondary"]
    Enum.at(colors, rem(index, length(colors)))
  end

  @doc """
  Reorders a list by moving an item from old_index to new_index.
  """
  def reorder_list(list, old_index, new_index)
      when old_index >= 0 and new_index >= 0 and old_index < length(list) and
             new_index < length(list) do
    # Get the item to move
    item = Enum.at(list, old_index)

    # Remove the item from its old position
    list_without_item = List.delete_at(list, old_index)

    # Insert the item at its new position
    List.insert_at(list_without_item, new_index, item)
  end

  # If indices are out of bounds, return the original list
  def reorder_list(list, _old_index, _new_index), do: list

  @doc """
  Generates a random string of the specified length.
  """
  def generate_random_string(length) do
    :crypto.strong_rand_bytes(length)
    |> Base.encode16(case: :lower)
    |> binary_part(0, length)
  end

  @doc """
  Generates a random connection name.
  """
  def generate_random_connection_name do
    prefix = Faker.Person.first_name()
    suffix = generate_random_string(4)
    "#{prefix}_#{suffix}"
  end

  @doc """
  Parses integer values with a default.
  Special handling for empty strings and "null" to preserve empty values.
  """
  def parse_int(value, default) when is_binary(value) do
    # Special handling for empty strings and "null" to represent empty fields
    cond do
      value == "" ->
        nil

      value == "null" ->
        nil

      true ->
        case Integer.parse(value) do
          {int, _} -> int
          :error -> default
        end
    end
  end

  def parse_int(nil, default), do: default
  def parse_int(value, _default) when is_integer(value), do: value

  @doc """
  Checks if a connection set has any connections.

  ## Parameters

  - `set`: The connection set to check

  ## Returns

  - `true` if the set has at least one connection
  - `false` if the set has no connections
  """
  def has_connections?(set) do
    connections = Map.get(set, :connections, [])
    length(connections) > 0
  end

  @doc """
  Decrypts a connection password if it's encrypted.

  ## Parameters

  - `password`: The password string which may be encrypted

  ## Returns

  - The decrypted password if it was encrypted
  - The original password if it wasn't encrypted
  """
  def decrypt_connection_password(password) when is_binary(password) do
    Encryption.decrypt(password)
  end

  def decrypt_connection_password(password), do: password

  @doc """
  Prepares a connection for use with MQTT client by decrypting sensitive fields.

  ## Parameters

  - `connection`: The connection map with possibly encrypted fields

  ## Returns

  - A new map with decrypted fields, ready for use with MQTT client
  """
  def prepare_connection_for_mqtt(connection) when is_map(connection) do
    # Create a new map with decrypted password
    Map.update(connection, :password, "", &decrypt_connection_password/1)
  end

  def prepare_connection_for_mqtt(connection), do: connection
end
