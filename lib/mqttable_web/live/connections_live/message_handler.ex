defmodule MqttableWeb.ConnectionsLive.MessageHandler do
  @moduledoc """
  Handles MQTT message operations and trace management.

  This module handles:
  - Message sending and publishing
  - Message tracing and real-time updates
  - Trace message filtering and processing
  - Message result formatting and user feedback

  All functions follow functional programming principles with pattern matching,
  pure functions where possible, and proper error handling with tagged tuples.
  """

  require Logger

  import Phoenix.LiveView, only: [put_flash: 3, stream_insert: 4, stream: 3, push_event: 3]
  import Phoenix.Component, only: [assign: 3]

  alias Mqttable.Templating.Engine
  alias Mqttable.ConnectionSets

  # Type definitions
  @type socket :: Phoenix.LiveView.Socket.t()
  @type connection_sets :: [map()]

  @doc """
  Handles opening the send message modal.

  Loads the latest form state for the current broker when opening the modal.
  """
  @spec handle_open_send_modal(socket()) :: {:noreply, socket()}
  def handle_open_send_modal(socket) do
    active_connection_set = socket.assigns[:active_connection_set]
    send_modal_form_state = load_send_modal_form_state(active_connection_set)

    socket =
      socket
      |> assign(:show_send_modal, true)
      |> assign(:send_modal_form_state, send_modal_form_state)

    {:noreply, socket}
  end

  @doc """
  Handles closing the send message modal.
  """
  @spec handle_close_send_modal(socket()) :: {:noreply, socket()}
  def handle_close_send_modal(socket) do
    {:noreply, assign(socket, :show_send_modal, false)}
  end

  @doc """
  Handles direct send message action without opening modal.
  Uses the current form state to send the message.
  """
  @spec handle_send_message_direct(socket()) :: {:noreply, socket()}
  def handle_send_message_direct(socket) do
    # Get current form state for the active broker
    active_connection_set = socket.assigns[:active_connection_set]
    send_modal_form_state = load_send_modal_form_state(active_connection_set)

    # Validate required fields
    client_id = send_modal_form_state["client_id"] || ""
    topic = String.trim(send_modal_form_state["topic"] || "")

    # Get payload and validate it
    payload_format = send_modal_form_state["payload_format"] || "text"
    payload = get_current_payload_for_format(send_modal_form_state)

    # Validate payload based on format
    payload_validation_result = validate_payload_for_format(payload, payload_format)

    if client_id != "" && topic != "" && payload_validation_result == :ok do
      # Send the message using the SendMessageModalComponent logic
      # We'll send a message to the component to trigger the send action
      send(self(), {:send_message_direct, send_modal_form_state})
      {:noreply, socket}
    else
      # Show error flash message for missing required fields or validation errors
      error_message =
        cond do
          client_id == "" ->
            "Please select a client before sending"

          topic == "" ->
            "Please enter a topic before sending"

          payload_validation_result != :ok ->
            "Payload validation failed: #{payload_validation_result}"

          true ->
            "Please fill in all required fields before sending"
        end

      socket = put_flash(socket, :error, error_message)
      {:noreply, socket}
    end
  end

  @doc """
  Handles opening the message detail modal.
  """
  @spec handle_open_detail_modal(socket(), map()) :: {:noreply, socket()}
  def handle_open_detail_modal(socket, message) do
    socket =
      socket
      |> assign(:show_detail_modal, true)
      |> assign(:detail_modal_message, message)

    {:noreply, socket}
  end

  @doc """
  Handles closing the message detail modal.
  """
  @spec handle_close_detail_modal(socket()) :: {:noreply, socket()}
  def handle_close_detail_modal(socket) do
    socket =
      socket
      |> assign(:show_detail_modal, false)
      |> assign(:detail_modal_message, nil)

    {:noreply, socket}
  end

  @doc """
  Handles switching payload view type in message details.
  """
  @spec handle_switch_payload_view(socket(), String.t()) :: {:noreply, socket()}
  def handle_switch_payload_view(socket, type) do
    valid_types = ["plaintext", "json", "hex"]
    payload_view_type = if type in valid_types, do: type, else: "plaintext"
    {:noreply, assign(socket, :payload_view_type, payload_view_type)}
  end

  @doc """
  Handles copying payload to clipboard.
  """
  @spec handle_copy_payload(socket(), String.t()) :: {:noreply, socket()}
  def handle_copy_payload(socket, payload) do
    {:noreply, push_event(socket, "copy_to_clipboard", %{text: payload})}
  end

  @doc """
  Handles MQTT trace message notifications.

  Adds trace messages to the stream if they belong to the currently active broker.
  """
  @spec handle_mqtt_trace_message(socket(), map()) :: {:noreply, socket()}
  def handle_mqtt_trace_message(socket, trace_message) do
    # Skip processing if settings modal is open to avoid disrupting user interaction
    if socket.assigns[:show_settings_modal] do
      {:noreply, socket}
    else
      active_broker_name = get_active_broker_name(socket)

      should_add_message =
        should_add_trace_message?(
          trace_message,
          active_broker_name,
          socket.assigns.connection_sets
        )

      if should_add_message do
        # Duplicate detection is handled on the client-side in trace-slick-grid-hook.js
        # to avoid issues with LiveStream enumeration
        trace_message = Mqttable.TraceManager.truncate_payload_for_grid(trace_message)
        {:noreply, stream_insert(socket, :trace_messages, trace_message, at: 0)}
      else
        {:noreply, socket}
      end
    end
  end

  @doc """
  Handles clearing trace messages for a specific broker.
  """
  @spec handle_clear_trace_messages(socket(), String.t()) :: {:noreply, socket()}
  def handle_clear_trace_messages(socket, broker_name) do
    active_broker_name = get_active_broker_name(socket)

    if broker_name == active_broker_name do
      {:noreply, stream(socket, :trace_messages, [])}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles successful message sending notifications.
  """
  @spec handle_message_sent_successfully(socket(), map()) :: {:noreply, socket()}
  def handle_message_sent_successfully(socket, packet_info) do
    {message, flash_type} = format_publish_result(packet_info)

    socket =
      case flash_type do
        :error ->
          put_flash(socket, :error, message)

        :warning ->
          put_flash(socket, :warning, message)

        :info ->
          socket
          |> put_flash(:info, message)
          |> push_event("auto_dismiss_flash", %{kind: "info", delay: 5000})
      end

    {:noreply, socket}
  end

  @doc """
  Handles message sending error notifications.
  """
  @spec handle_message_send_error(socket(), String.t()) :: {:noreply, socket()}
  def handle_message_send_error(socket, error_message) do
    socket = put_flash(socket, :error, error_message)
    {:noreply, socket}
  end

  @doc """
  Handles updating send modal form state.
  """
  @spec handle_update_send_modal_form(socket(), map(), String.t() | nil) :: {:noreply, socket()}
  def handle_update_send_modal_form(socket, form_state, broker_name \\ nil) do
    # Save form state to ui_state for the specified broker or current broker
    target_broker_name = broker_name || get_active_broker_name(socket)

    if target_broker_name do
      Mqttable.ConnectionSets.update_send_modal_form_state(target_broker_name, form_state)
    end

    # Only update socket if form state actually changed to prevent unnecessary re-renders
    current_form_state = socket.assigns[:send_modal_form_state]

    if current_form_state != form_state do
      {:noreply, assign(socket, :send_modal_form_state, form_state)}
    else
      {:noreply, socket}
    end
  end

  @doc """
  Handles payload editor changes.
  """
  @spec handle_payload_editor_changed(socket(), String.t(), String.t()) :: {:noreply, socket()}
  def handle_payload_editor_changed(socket, payload, payload_format) do
    current_form_state = socket.assigns[:send_modal_form_state] || %{}

    updated_form_state =
      current_form_state
      |> Map.put("payload_format", payload_format)
      |> update_format_specific_payload_in_form(payload_format, payload)

    # Save form state to ui_state for the current broker
    active_connection_set = socket.assigns[:active_connection_set]
    broker_name = if active_connection_set, do: active_connection_set.name, else: nil

    if broker_name do
      Mqttable.ConnectionSets.update_send_modal_form_state(broker_name, updated_form_state)
    end

    {:noreply, assign(socket, :send_modal_form_state, updated_form_state)}
  end

  @doc """
  Handles grid data update events.
  """
  @spec handle_push_grid_data_update(socket(), map()) :: {:noreply, socket()}
  def handle_push_grid_data_update(socket, data) do
    socket = push_event(socket, "grid_data_update", data)
    {:noreply, socket}
  end

  @doc """
  Handles export data events.
  """
  @spec handle_push_export_event(socket(), map()) :: {:noreply, socket()}
  def handle_push_export_event(socket, data) do
    socket = push_event(socket, "export_data", data)
    {:noreply, socket}
  end

  # Private functions

  @spec get_active_broker_name(socket()) :: String.t() | nil
  defp get_active_broker_name(socket) do
    if socket.assigns.active_connection_set do
      socket.assigns.active_connection_set.name
    else
      nil
    end
  end

  # Get the current payload based on the selected format
  defp get_current_payload_for_format(form) do
    format = Map.get(form, "payload_format", "text")

    case format do
      "text" -> Map.get(form, "payload_text", "")
      "json" -> Map.get(form, "payload_json", "")
      "hex" -> Map.get(form, "payload_hex", "")
      "file" -> Map.get(form, "payload_file", "")
      _ -> Map.get(form, "payload_text", "")
    end
  end

  # Validate payload based on format
  defp validate_payload_for_format(payload, format) do
    case format do
      "json" -> validate_json_payload(payload)
      "hex" -> validate_hex_payload(payload)
      "file" -> validate_file_payload(payload)
      "text" -> :ok
      _ -> :ok
    end
  end

  defp validate_json_payload(""), do: :ok

  defp validate_json_payload(payload) when is_binary(payload) do
    # Check if payload contains template syntax
    if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
      # For templates, try to render and validate the result
      case Engine.render(payload, %{}, %{}) do
        {:ok, rendered} ->
          case Jason.decode(rendered) do
            {:ok, _} -> :ok
            {:error, _} -> "JSON template renders to invalid JSON"
          end

        {:error, _} ->
          # Template has syntax errors, but we'll let the template processor handle that
          :ok
      end
    else
      # For non-templates, validate directly
      case Jason.decode(payload) do
        {:ok, _} -> :ok
        {:error, _} -> "Invalid JSON format"
      end
    end
  end

  defp validate_hex_payload(""), do: :ok

  defp validate_hex_payload(payload) when is_binary(payload) do
    # Remove whitespace and check for valid hex
    cleaned = String.replace(payload, ~r/\s/, "")

    cond do
      cleaned == "" ->
        :ok

      not String.match?(cleaned, ~r/^[0-9A-Fa-f]*$/) ->
        "Invalid hex format. Use only 0-9, A-F characters"

      rem(String.length(cleaned), 2) != 0 ->
        "Hex payload must have even number of characters"

      true ->
        :ok
    end
  end

  defp validate_file_payload(""), do: "Please upload a file or select a different format"
  defp validate_file_payload(_), do: :ok

  @spec should_add_trace_message?(map(), String.t() | nil, connection_sets()) :: boolean()
  defp should_add_trace_message?(trace_message, active_broker_name, connection_sets) do
    if active_broker_name do
      case find_broker_by_client_id(trace_message.client_id, connection_sets) do
        {:ok, broker_name} -> broker_name == active_broker_name
        {:error, _} -> false
      end
    else
      false
    end
  end

  @spec find_broker_by_client_id(String.t(), connection_sets()) ::
          {:ok, String.t()} | {:error, :broker_not_found}
  defp find_broker_by_client_id(client_id, connection_sets) do
    connection_sets = connection_sets || []

    result =
      Enum.find_value(connection_sets, fn set ->
        connection =
          Enum.find(set[:connections] || [], fn conn ->
            Map.get(conn, :client_id) == client_id
          end)

        if connection do
          set[:name]
        else
          nil
        end
      end)

    case result do
      nil -> {:error, :broker_not_found}
      broker_name -> {:ok, broker_name}
    end
  end

  @spec format_publish_result(any()) :: {String.t(), atom()}
  defp format_publish_result(packet_info) do
    case packet_info do
      # QoS 0 messages return simple integer packet_id (usually 0)
      packet_id when is_integer(packet_id) ->
        {"Message sent successfully (Packet ID: #{packet_id})", :info}

      # QoS 1/2 messages return a map with detailed information
      %{packet_id: packet_id, reason_code: reason_code, reason_code_name: reason_name} ->
        case reason_code do
          0 ->
            {"Message sent successfully (Packet ID: #{packet_id})", :info}

          # Error codes that indicate failure
          code
          when code in [
                 16,
                 17,
                 18,
                 19,
                 128,
                 129,
                 130,
                 131,
                 132,
                 133,
                 134,
                 135,
                 136,
                 137,
                 138,
                 139,
                 140,
                 141,
                 142,
                 143
               ] ->
            reason_text = format_reason_code_name(reason_name)
            {"Message delivery failed: #{reason_text} (Code: #{code})", :error}

          # Warning codes (informational but not necessarily errors)
          code when code in [1, 2, 3, 4] ->
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

          code ->
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
        end

      # Fallback for unexpected format
      other ->
        {"Message sent (Response: #{inspect(other)})", :info}
    end
  end

  @spec format_reason_code_name(atom() | any()) :: String.t()
  defp format_reason_code_name(reason_name) when is_atom(reason_name) do
    reason_name
    |> Atom.to_string()
    |> String.replace("_", " ")
    |> String.capitalize()
  end

  defp format_reason_code_name(other), do: inspect(other)

  @spec load_send_modal_form_state(map() | nil) :: map()
  defp load_send_modal_form_state(active_connection_set) do
    if active_connection_set do
      broker_name = active_connection_set.name
      stored_form_state = ConnectionSets.get_send_modal_form_state(broker_name)

      if stored_form_state do
        ensure_format_specific_payloads_in_form(stored_form_state)
      else
        default_publish_form()
      end
    else
      default_publish_form()
    end
  end

  @spec default_publish_form() :: map()
  defp default_publish_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload_format" => "text",
      "qos" => 0,
      "retain" => false,
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => 0,
      "topic_alias" => 0,
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => [],
      # Format-specific payloads
      "payload_text" => "",
      "payload_json" => "",
      "payload_hex" => "",
      "payload_file" => ""
    }
  end

  @spec update_format_specific_payload_in_form(map(), String.t(), String.t()) :: map()
  defp update_format_specific_payload_in_form(form_state, format, payload) do
    case format do
      "text" -> Map.put(form_state, "payload_text", payload)
      "json" -> Map.put(form_state, "payload_json", payload)
      "hex" -> Map.put(form_state, "payload_hex", payload)
      "file" -> Map.put(form_state, "payload_file", payload)
      _ -> Map.put(form_state, "payload_text", payload)
    end
  end

  @spec ensure_format_specific_payloads_in_form(map()) :: map()
  defp ensure_format_specific_payloads_in_form(form_state) do
    form_state
    |> Map.put_new("payload_text", "")
    |> Map.put_new("payload_json", "")
    |> Map.put_new("payload_hex", "")
    |> Map.put_new("payload_file", "")
  end
end
